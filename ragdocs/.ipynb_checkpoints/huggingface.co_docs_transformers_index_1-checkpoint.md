[![Hugging Face's logo](https://huggingface.co/front/assets/huggingface_logo-noborder.svg) Hugging Face](https://huggingface.co/)
  * [ Models](https://huggingface.co/models)
  * [ Datasets](https://huggingface.co/datasets)
  * [ Spaces](https://huggingface.co/spaces)
  * Community 
  * [ Docs](https://huggingface.co/docs)
  * [ Enterprise](https://huggingface.co/enterprise)
  * [Pricing](https://huggingface.co/pricing)
  * [Log In](https://huggingface.co/login)
  * [Sign Up](https://huggingface.co/join)


Transformers documentation
Transformers
# Transformers
🏡 View all docsAWS Trainium & InferentiaAccelerateAmazon SageMakerArgillaAutoTrainBitsandbytesChat UIDataset viewerDatasetsDiffusersDistilabelEvaluateGradioHubHub Python LibraryHuggingface.jsInference Endpoints (dedicated)Inference ProvidersLeaderboardsLightevalOptimumPEFTSafetensorsSentence TransformersTRLTasksText Embeddings InferenceText Generation InferenceTokenizersTransformersTransformers.jssmolagentstimm
Search documentation
`⌘K`
mainv4.52.3v4.51.3v4.50.0v4.49.0v4.48.2v4.47.1v4.46.3v4.45.2v4.44.2v4.43.4v4.42.4v4.41.2v4.40.2v4.39.3v4.38.2v4.37.2v4.36.1v4.35.2v4.34.1v4.33.3v4.32.1v4.31.0v4.30.0v4.29.1v4.28.1v4.27.2v4.26.1v4.25.1v4.24.0v4.23.1v4.22.2v4.21.3v4.20.1v4.19.4v4.18.0v4.17.0v4.16.2v4.15.0v4.14.1v4.13.0v4.12.5v4.11.3v4.10.1v4.9.2v4.8.2v4.7.0v4.6.0v4.5.1v4.4.2v4.3.3v4.2.2v4.1.1v4.0.1v3.5.1v3.4.0v3.3.1v3.2.0v3.1.0v3.0.2v2.11.0v2.10.0v2.9.1v2.8.0v2.7.0v2.6.0v2.5.1v2.4.1v2.3.0v2.2.2v2.1.1v2.0.0v1.2.0v1.1.0v1.0.0doc-builder-html ARDEENESFRHIITJAKOPTTETRZH [ ](https://github.com/huggingface/transformers)
Get started
[Transformers ](https://huggingface.co/docs/transformers/index)[Installation ](https://huggingface.co/docs/transformers/installation)[Quickstart ](https://huggingface.co/docs/transformers/quicktour)
Base classes
Inference
Training
Quantization
Export to production
Resources
Contribute
API
![Hugging Face's logo](https://huggingface.co/front/assets/huggingface_logo-noborder.svg)
Join the Hugging Face community
and get access to the augmented documentation experience 
Collaborate on models, datasets and Spaces 
Faster examples with accelerated inference 
Switch between documentation themes 
[Sign Up](https://huggingface.co/join)
to get started
# [](https://huggingface.co/docs/transformers/index#transformers) Transformers
Transformers is a library of pretrained natural language processing, computer vision, audio, and multimodal models for inference and training. Use Transformers to train models on your data, build inference applications, and generate text with large language models.
Explore the [Hugging Face Hub](https://huggingface.com) today to find a model and use Transformers to help you get started right away.
## [](https://huggingface.co/docs/transformers/index#features) Features
Transformers provides everything you need for inference or training with state-of-the-art pretrained models. Some of the main features include:
  * [Pipeline](https://huggingface.co/docs/transformers/pipeline_tutorial): Simple and optimized inference class for many machine learning tasks like text generation, image segmentation, automatic speech recognition, document question answering, and more.
  * [Trainer](https://huggingface.co/docs/transformers/trainer): A comprehensive trainer that supports features such as mixed precision, torch.compile, and FlashAttention for training and distributed training for PyTorch models.
  * [generate](https://huggingface.co/docs/transformers/llm_tutorial): Fast text generation with large language models (LLMs) and vision language models (VLMs), including support for streaming and multiple decoding strategies.


## [](https://huggingface.co/docs/transformers/index#design) Design
Read our [Philosophy](https://huggingface.co/docs/transformers/philosophy) to learn more about Transformers’ design principles.
Transformers is designed for developers and machine learning engineers and researchers. Its main design principles are:
  1. Fast and easy to use: Every model is implemented from only three main classes (configuration, model, and preprocessor) and can be quickly used for inference or training with [Pipeline](https://huggingface.co/docs/transformers/v4.52.3/en/main_classes/pipelines#transformers.Pipeline) or [Trainer](https://huggingface.co/docs/transformers/v4.52.3/en/main_classes/trainer#transformers.Trainer).
  2. Pretrained models: Reduce your carbon footprint, compute cost and time by using a pretrained model instead of training an entirely new one. Each pretrained model is reproduced as closely as possible to the original model and offers state-of-the-art performance.


[![HuggingFace Expert Acceleration Program](https://hf.co/datasets/huggingface/documentation-images/resolve/81d7d9201fd4ceb537fc4cebc22c29c37a2ed216/transformers/transformers-index.png)](https://huggingface.co/support)
[< > Update on GitHub](https://github.com/huggingface/transformers/blob/main/docs/source/en/index.md)
[Installation→](https://huggingface.co/docs/transformers/installation)
[Transformers](https://huggingface.co/docs/transformers/index#transformers) [Features](https://huggingface.co/docs/transformers/index#features) [Design](https://huggingface.co/docs/transformers/index#design)
